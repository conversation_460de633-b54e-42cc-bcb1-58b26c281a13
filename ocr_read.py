import os
import json
from pathlib import Path
from typing import List, Dict


def scan_files(input_path: str) -> List[Dict[str, str]]:
    """
    扫描指定路径下的所有文件，返回文件信息的JSON数组

    Args:
        input_path (str): 要扫描的文件夹路径

    Returns:
        List[Dict[str, str]]: 包含文件信息的JSON数组，格式为：
        [
            {
                "fileName": "文件名",
                "filePath": "文件全路径",
                "parentFileName": "父级文件名"
            }
        ]
    """
    result = []

    # 检查输入路径是否存在
    if not os.path.exists(input_path):
        print(f"错误：路径 {input_path} 不存在")
        return result

    # 检查输入路径是否为目录
    if not os.path.isdir(input_path):
        print(f"错误：路径 {input_path} 不是一个目录")
        return result

    # 使用os.walk遍历所有子目录和文件
    for root, dirs, files in os.walk(input_path):
        # 获取当前目录的父目录名
        parent_dir_name = os.path.basename(root)

        # 遍历当前目录下的所有文件
        for file in files:
            # 构建完整的文件路径
            full_path = os.path.join(root, file)

            # 创建文件信息字典
            file_info = {
                "fileName": file,
                "filePath": full_path,
                "parentFileName": parent_dir_name
            }

            result.append(file_info)

    return result


def scan_files_to_json(input_path: str) -> str:
    """
    扫描指定路径下的所有文件，返回JSON字符串

    Args:
        input_path (str): 要扫描的文件夹路径

    Returns:
        str: JSON格式的字符串
    """
    file_list = scan_files(input_path)
    return json.dumps(file_list, ensure_ascii=False, indent=2)


def create_json_files(file_list: List[Dict[str, str]], target_dir: str) -> bool:
    """
    根据文件列表在目标目录中创建对应的JSON文件

    Args:
        file_list (List[Dict[str, str]]): 文件信息列表
        target_dir (str): 目标目录路径

    Returns:
        bool: 创建成功返回True，失败返回False
    """
    try:
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)

        # 循环处理每个文件信息
        for index, file_info in enumerate(file_list):
            # 生成JSON文件名，使用索引和原文件名
            json_filename = f"{index + 1:04d}_{file_info['fileName']}.json"
            json_filepath = os.path.join(target_dir, json_filename)

            # 将文件信息写入JSON文件
            with open(json_filepath, 'w', encoding='utf-8') as json_file:
                json.dump(file_info, json_file, ensure_ascii=False, indent=2)

            print(f"已创建JSON文件: {json_filepath}")

        print(f"\n成功创建了 {len(file_list)} 个JSON文件到目录: {os.path.abspath(target_dir)}")
        return True

    except Exception as e:
        print(f"创建JSON文件时出错: {str(e)}")
        return False


def process_directory_to_json_files(input_path: str, target_dir: str = "output_json") -> bool:
    """
    完整的处理流程：扫描目录并创建对应的JSON文件

    Args:
        input_path (str): 要扫描的源目录路径
        target_dir (str): 目标JSON文件存放目录，默认为"output_json"

    Returns:
        bool: 处理成功返回True，失败返回False
    """
    print(f"开始处理目录: {os.path.abspath(input_path)}")

    # 扫描文件
    file_list = scan_files(input_path)

    if not file_list:
        print("未找到任何文件")
        return False

    print(f"找到 {len(file_list)} 个文件")

    # 创建JSON文件
    success = create_json_files(file_list, target_dir)

    return success


def main_process(input_path: str, output_dir: str = "output_json"):
    """
    主要处理函数，供外部调用

    Args:
        input_path (str): 要扫描的源目录路径
        output_dir (str): 输出JSON文件的目录，默认为"output_json"

    Returns:
        List[Dict[str, str]]: 返回文件信息列表
    """
    # 扫描文件获取JSON数组
    file_list = scan_files(input_path)

    if file_list:
        # 创建对应的JSON文件
        create_json_files(file_list, output_dir)
        print(f"处理完成！共处理了 {len(file_list)} 个文件")
    else:
        print("未找到任何文件")

    return file_list


if __name__ == "__main__":
    # 示例用法
    test_path = "/Users/<USER>/Downloads/2025年周报"  # 您提到的示例路径

    # 如果测试路径不存在，使用当前目录作为示例
    if not os.path.exists(test_path):
        test_path = "."
        print(f"测试路径不存在，使用当前目录 {os.path.abspath(test_path)} 进行测试")

    print(f"正在扫描路径: {os.path.abspath(test_path)}")

    # 获取文件列表
    file_list = scan_files(test_path)

    # 打印结果
    print(f"\n找到 {len(file_list)} 个文件:")
    print(json.dumps(file_list, ensure_ascii=False, indent=2))

    # 新增功能：创建JSON文件
    print("\n" + "="*50)
    print("开始创建JSON文件...")

    # 指定目标目录
    target_directory = "output_json"

    # 执行完整的处理流程
    success = process_directory_to_json_files(test_path, target_directory)

    if success:
        print(f"\n✅ 处理完成！JSON文件已保存到: {os.path.abspath(target_directory)}")
    else:
        print("\n❌ 处理失败！")

