#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR读取文件使用示例
"""

from ocr_read import main_process, scan_files, create_json_files

def example_usage():
    """
    使用示例
    """
    print("=== OCR文件扫描和JSON生成示例 ===\n")
    
    # 示例1：使用简单的主处理函数
    print("示例1：使用main_process函数")
    input_directory = "."  # 当前目录
    output_directory = "example_output"
    
    file_list = main_process(input_directory, output_directory)
    print(f"返回的文件列表包含 {len(file_list)} 个文件\n")
    
    # 示例2：分步骤处理
    print("示例2：分步骤处理")
    
    # 第一步：扫描文件
    print("第一步：扫描文件...")
    files = scan_files(input_directory)
    print(f"扫描到 {len(files)} 个文件")
    
    # 第二步：创建JSON文件
    if files:
        print("第二步：创建JSON文件...")
        success = create_json_files(files, "step_by_step_output")
        if success:
            print("✅ JSON文件创建成功！")
        else:
            print("❌ JSON文件创建失败！")
    
    print("\n=== 示例完成 ===")

if __name__ == "__main__":
    example_usage()
